{"compilerOptions": {"module": "CommonJS", "target": "ES2020", "outDir": "../out", "lib": ["ES2020"], "sourceMap": true, "rootDir": "../src", "strict": true, "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": "../", "paths": {"@shared/*": ["src/shared/*"], "@extension/*": ["src/extension/*"]}}, "include": ["../src/**/*"], "exclude": ["../node_modules", "../webview", "../out"]}