# Dependencies
node_modules/
/node_modules
webview/node_modules/

# Build output
/out
/dist
*.vsix

# VS Code extension testing
.vscode-test/
.vscode-test.mjs

# Vite cache and build artifacts
.vite/
webview/.vite/

# Package manager files (keep package-lock.json for consistency)
yarn.lock
pnpm-lock.yaml
.pnpm-debug.log*

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Editor directories and files
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*~

# Temporary files
*.tmp
*.temp
.cache/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# AI tools
.codebuddy/

# Deployment
.vercel/

# Large test files and binary data
*.mipi_raw
tools/mipi10-converter/*.raw
tools/mipi10-converter/*.mipi_raw

# TypeScript
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/

# Storybook build outputs
.out
.storybook-out

# Uncomment the public line in if your project uses Gatsby and not Next.js
# public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*