{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": "build", "presentation": {"panel": "shared", "reveal": "silent"}, "problemMatcher": "$tsc"}, {"label": "build-all", "dependsOrder": "sequence", "dependsOn": ["npm: compile", "npm: build-webview"], "group": {"kind": "build", "isDefault": true}, "presentation": {"panel": "shared", "reveal": "always"}}, {"type": "npm", "script": "build-webview", "group": "build", "presentation": {"panel": "shared", "reveal": "silent"}}, {"type": "npm", "script": "watch-extension", "isBackground": true, "group": "build", "presentation": {"panel": "shared", "reveal": "silent"}, "problemMatcher": "$tsc-watch"}]}