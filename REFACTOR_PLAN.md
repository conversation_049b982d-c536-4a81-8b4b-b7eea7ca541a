# Raw Image Viewer 重构计划

## 当前问题
1. 文件组织混乱：media/ 实际是webview代码，命名误导
2. 职责不清：扩展逻辑和UI逻辑混在一起
3. 配置分散：各种配置文件散落根目录
4. 缺乏模块化：没有清晰的层次结构

## 新的目录结构

```
raw-image-viewer/
├── src/                          # VSCode扩展核心代码
│   ├── extension/                # 扩展主体
│   │   ├── extension.ts         # 扩展入口
│   │   ├── provider.ts          # 自定义编辑器提供者
│   │   └── document.ts          # 文档模型
│   ├── shared/                  # 扩展和webview共享代码
│   │   ├── types.ts            # 类型定义
│   │   ├── constants.ts        # 常量定义
│   │   └── utils.ts            # 工具函数
│   └── test/                   # 测试代码
│       └── extension.test.ts
├── webview/                     # Vue webview应用
│   ├── src/
│   │   ├── components/         # Vue组件
│   │   │   ├── controls/       # 控制面板相关组件
│   │   │   ├── viewer/         # 图像查看器组件
│   │   │   └── status/         # 状态栏组件
│   │   ├── stores/            # Pinia状态管理
│   │   ├── utils/             # webview工具函数
│   │   ├── App.vue            # 根组件
│   │   └── main.js            # 入口文件
│   ├── public/
│   │   └── index.html
│   └── vite.config.js
├── examples/                   # 示例文件
├── tools/                      # 工具脚本
│   ├── mipi10-converter/      # MIPI10转换工具
│   └── release.js             # 发布脚本
├── config/                     # 配置文件
│   ├── .eslintrc.json
│   ├── tsconfig.json
│   └── vite.config.js
└── docs/                      # 文档
    ├── README.md
    ├── CHANGELOG.md
    └── RELEASE.md
```

## 重构原则
1. 单一职责：每个文件只做一件事
2. 清晰边界：扩展逻辑和UI逻辑完全分离
3. 零破坏性：保持所有现有API不变
4. 可读性优先：文件名和目录名见名知意